package org.jeecg.modules.activation_code.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.activation_code.entity.ActivationCode;
import org.jeecg.modules.batch.entity.ActivationCodeBatch;

/**
 * @Description: activation_code
 * @Author: jeecg-boot
 * @Date:   2025-05-17
 * @Version: V1.0
 */
public interface IActivationCodeService extends IService<ActivationCode> {

    void generateCode(ActivationCodeBatch activationCodeBatch);
}
