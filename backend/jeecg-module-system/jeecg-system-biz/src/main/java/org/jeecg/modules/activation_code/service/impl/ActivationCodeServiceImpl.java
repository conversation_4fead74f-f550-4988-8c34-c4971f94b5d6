package org.jeecg.modules.activation_code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.modules.activation_code.entity.ActivationCode;
import org.jeecg.modules.activation_code.mapper.ActivationCodeMapper;
import org.jeecg.modules.activation_code.service.IActivationCodeService;
import org.jeecg.modules.batch.entity.ActivationCodeBatch;
import org.jeecg.modules.batch.service.IActivationCodeBatchService;
import org.jeecg.modules.digital_textbook.entity.DigitalTextbook;
import org.jeecg.modules.digital_textbook.service.IDigitalTextbookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.SecureRandom;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @Description: activation_code
 * @Author: jeecg-boot
 * @Date: 2025-05-17
 * @Version: V1.0
 */
@Service
public class ActivationCodeServiceImpl extends ServiceImpl<ActivationCodeMapper, ActivationCode> implements IActivationCodeService {

    private static final String LETTERS = "ABCDEFGHJKLMNPQRSTUVWXYZ";
    private static final String DIGITS = "23456789";
    private static final SecureRandom random = new SecureRandom();


    @Value("${jeecg.path.upload}")
    private String upLoadPath;

    private static final int BATCH_SIZE = 10000;

    @Async
    @Override
    public void generateCode(ActivationCodeBatch activationCodeBatch) {
        int total = activationCodeBatch.getCodeCount();
        String prefix = activationCodeBatch.getCodePrefix();

        Set<String> codeSet = ConcurrentHashMap.newKeySet(total);
        int genThreads = Runtime.getRuntime().availableProcessors();
        ExecutorService genExecutor = Executors.newFixedThreadPool(genThreads);
        CountDownLatch genLatch = new CountDownLatch(genThreads);

        for (int i = 0; i < genThreads; i++) {
            genExecutor.submit(() -> {
                while (codeSet.size() < total) {
                    String code = generateCodeString(prefix);
                    codeSet.add(code);
                }
                genLatch.countDown();
            });
        }

        try {
            genLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        genExecutor.shutdown();

        List<ActivationCode> codeList = codeSet.stream().limit(total).map(code -> {
            ActivationCode ac = new ActivationCode();
            ac.setCode(code);
            ac.setIsUsed(0);
            ac.setExpireTime(activationCodeBatch.getEndTime());
            ac.setDigitextId(activationCodeBatch.getTextbookId());
            ac.setBatchId(activationCodeBatch.getId());
            return ac;
        }).collect(Collectors.toList());

        // 异步分批写库
        int batchCount = (codeList.size() + BATCH_SIZE - 1) / BATCH_SIZE;
        ExecutorService writeExecutor = Executors.newFixedThreadPool(Math.min(batchCount, 10));
        CountDownLatch writeLatch = new CountDownLatch(batchCount);

        for (int i = 0; i < codeList.size(); i += BATCH_SIZE) {
            int start = i;
            int end = Math.min(i + BATCH_SIZE, codeList.size());
            List<ActivationCode> batch = codeList.subList(start, end);

            writeExecutor.submit(() -> {
                try {
                    saveBatch(batch);
                } finally {
                    writeLatch.countDown();
                }
            });
        }

        try {
            writeLatch.await(); // 等待所有批次插入完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        writeExecutor.shutdown();
        saveCodesToCsv(codeList, activationCodeBatch.getId());
    }


    public static String generateCodeString(String prefix) {
        String body = IntStream.range(0, 3)
                .mapToObj(i -> generateSegment())
                .collect(Collectors.joining("-"));
        return prefix == null ? body : prefix + "-" + body;
    }

    private static String generateSegment() {
        char digit = DIGITS.charAt(random.nextInt(DIGITS.length()));
        List<Character> chars = IntStream.range(0, 4)
                .mapToObj(i -> LETTERS.charAt(random.nextInt(LETTERS.length())))
                .collect(Collectors.toList());
        chars.add(digit);
        Collections.shuffle(chars);
        return chars.stream().map(String::valueOf).collect(Collectors.joining());
    }
    @Lazy
    @Autowired
    private IDigitalTextbookService digitalTextbookService;

    @Lazy
    @Autowired
    private IActivationCodeBatchService activationCodeBatchService;

    private void saveCodesToCsv(List<ActivationCode> codeList, String batchId) {
        String fileName = "activation_codes_" + batchId + ".csv";
        Path filePath = Paths.get(upLoadPath, "activation_codes", fileName); // 改为 jeecg.path.upload
        ActivationCodeBatch batchInfo = activationCodeBatchService.getById(batchId);
        if (batchInfo == null) {
            throw new JeecgBootException("批次不存在");
        }
        DigitalTextbook textbook = digitalTextbookService.getOne(new QueryWrapper<DigitalTextbook>().lambda().eq(DigitalTextbook::getId, batchInfo.getTextbookId()));
        try {
            // 确保目录存在
            Files.createDirectories(filePath.getParent());

            try (BufferedWriter writer = Files.newBufferedWriter(filePath);
                 CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT
                         .withHeader("激活码", "关联图书","批次名称","是否被使用","激活时间"))) {

                for (ActivationCode ac : codeList) {
                    csvPrinter.printRecord(
                            ac.getCode(),
                            textbook.getName(),
                            batchInfo.getBatchName(),
                            ac.getIsUsed() == 1 ? "是" : "否",
                            ac.getUsedTime()
                    );
                }
                csvPrinter.flush();
            }

            System.out.println("✅ 激活码 CSV 文件已保存: " + filePath);

        } catch (IOException e) {
            e.printStackTrace();
            System.err.println("❌ 保存激活码 CSV 文件失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActivationCode findAndLockByCode(String inputCode) {
        if (inputCode == null || inputCode.trim().isEmpty()) {
            return null;
        }

        // 标准化输入的激活码 - 只保留字母和数字，转为大写
        String normalizedCode = inputCode.replaceAll("[^a-zA-Z0-9]", "").toUpperCase();

        if (normalizedCode.isEmpty()) {
            return null;
        }

        // 直接查询并加锁，一次性完成所有验证
        return baseMapper.findByNormalizedCodeWithLock(normalizedCode);
    }

}
