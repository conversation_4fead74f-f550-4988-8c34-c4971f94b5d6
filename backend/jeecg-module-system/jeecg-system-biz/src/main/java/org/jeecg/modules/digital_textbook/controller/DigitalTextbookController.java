package org.jeecg.modules.digital_textbook.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.pdfbox.cos.COSBase;
import org.apache.pdfbox.cos.COSDictionary;
import org.apache.pdfbox.cos.COSName;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentCatalog;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.interactive.annotation.PDAnnotation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.tika.Tika;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.activation_code.entity.ActivationCode;
import org.jeecg.modules.activation_code.service.IActivationCodeService;
import org.jeecg.modules.batch.entity.ActivationCodeBatch;
import org.jeecg.modules.batch.service.IActivationCodeBatchService;
import org.jeecg.modules.digital_textbook.entity.AddTextBookDto;
import org.jeecg.modules.digital_textbook.entity.DigitalTextbook;
import org.jeecg.modules.digital_textbook.service.IDigitalTextbookService;
import org.jeecg.modules.digital_textbook_content.entity.DigitalTextbookContent;
import org.jeecg.modules.digital_textbook_content.service.IDigitalTextbookContentService;
import org.jeecg.modules.school_stage.entity.SchoolStage;
import org.jeecg.modules.school_stage.service.ISchoolStageService;
import org.jeecg.modules.subject.entity.Subject;
import org.jeecg.modules.subject.service.ISubjectService;
import org.jeecg.modules.user_textbook.entity.UserTextbook;
import org.jeecg.modules.user_textbook.service.IUserTextbookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: digital_textbook
 * @Author: jeecg-boot
 * @Date: 2025-05-17
 * @Version: V1.0
 */
@Api(tags = "数字化教材管理")
@RestController
@RequestMapping("/digital_textbook/digitalTextbook")
@Slf4j
public class DigitalTextbookController extends JeecgController<DigitalTextbook, IDigitalTextbookService> {
    @Autowired
    private IDigitalTextbookService digitalTextbookService;

    @Autowired
    private IActivationCodeService activationCodeService;

    @Autowired
    protected IUserTextbookService userTextbookService;

    @Autowired
    protected IDigitalTextbookContentService iDigitalTextbookContentService;

    @Autowired
    protected ISchoolStageService schoolStageService;

    @Autowired
    protected ISubjectService subjectService;

    @Autowired
    protected IActivationCodeBatchService activationCodeBatchService;

    /**
     * 分页列表查询
     *
     * @param digitalTextbook
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "digital_textbook-分页列表查询")
    @ApiOperation(value = "管理后台-分页列表查询", notes = "管理后台-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<DigitalTextbook>> queryPageList(DigitalTextbook digitalTextbook,
                                                        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                        HttpServletRequest req) {
        // 1. 主表分页查询
        QueryWrapper<DigitalTextbook> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StringUtils.isNotBlank(digitalTextbook.getSubjectId()), DigitalTextbook::getSubjectId, digitalTextbook.getSubjectId());

        if (StringUtils.isNotBlank(digitalTextbook.getStageId())) {
            SchoolStage byId = schoolStageService.getById(digitalTextbook.getStageId());
            if (byId.getPid().equals("0")) {
                List<SchoolStage> schoolStages = schoolStageService.list(new QueryWrapper<SchoolStage>().lambda()
                        .eq(SchoolStage::getPid, digitalTextbook.getStageId()));
                List<String> stageIds = schoolStages.stream().map(SchoolStage::getId).collect(Collectors.toList());
                queryWrapper.lambda().in(DigitalTextbook::getStageId, stageIds);
            } else {
                queryWrapper.lambda().eq(StringUtils.isNotBlank(digitalTextbook.getStageId()), DigitalTextbook::getStageId, digitalTextbook.getStageId());
            }
        }
        queryWrapper.lambda().like(StringUtils.isNotBlank(digitalTextbook.getName()), DigitalTextbook::getName, digitalTextbook.getName());
        queryWrapper.lambda().orderByAsc(DigitalTextbook::getSort);
        Page<DigitalTextbook> page = new Page<>(pageNo, pageSize);
        IPage<DigitalTextbook> pageList = digitalTextbookService.page(page, queryWrapper);

        if (pageList.getTotal() == 0) {
            return Result.OK(pageList);
        }
        // 2. 提取所有关联ID（去重）
        List<String> subjectIds = pageList.getRecords().stream()
                .map(DigitalTextbook::getSubjectId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        List<String> stageIds = pageList.getRecords().stream()
                .map(DigitalTextbook::getStageId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 3. 批量查询关联数据（2次查询解决N+1问题）
        Map<String, Subject> subjectMap = subjectService.listByIds(subjectIds)
                .stream()
                .collect(Collectors.toMap(Subject::getId, Function.identity()));

        Map<String, SchoolStage> stageMap = schoolStageService.listByIds(stageIds)
                .stream()
                .collect(Collectors.toMap(SchoolStage::getId, Function.identity()));

        // 4. 内存关联数据
        pageList.getRecords().forEach(item -> {
            item.setSubjectName(subjectMap.get(item.getSubjectId()) != null ? subjectMap.get(item.getSubjectId()).getName() : "");
            item.setSchoolStageName(stageMap.get(item.getStageId()) != null ? stageMap.get(item.getStageId()).getName() : "");
        });
        return Result.OK(pageList);
    }

    @ApiOperation(value = "管理后台-查询列表无分页", notes = "管理后台-查询列表无分页")
    @GetMapping(value = "/getAll")
    public Result<List<DigitalTextbook>> getAll() {
        List<DigitalTextbook> digitalTextbookList = digitalTextbookService.list(new QueryWrapper<DigitalTextbook>().lambda().orderByAsc(DigitalTextbook::getSort));
        return Result.OK(digitalTextbookList);
    }


    /**
     * 添加
     *
     * @param digitalTextbook
     * @return
     */
    @AutoLog(value = "digital_textbook-添加")
    @ApiOperation(value = "管理后台-添加", notes = "管理后台-添加")
    @RequiresPermissions("digital_textbook:digital_textbook:add")
    @PostMapping(value = "/add")
    @Transactional
    public Result<String> add(@Valid @RequestBody DigitalTextbook digitalTextbook) {
        digitalTextbookService.save(digitalTextbook);
        DigitalTextbookContent digitalTextbookContent = new DigitalTextbookContent();
        digitalTextbookContent.setTextbookId(digitalTextbook.getId());
        iDigitalTextbookContentService.update(digitalTextbookContent, new QueryWrapper<DigitalTextbookContent>().lambda().eq(DigitalTextbookContent::getFileHash, digitalTextbook.getCode()));
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param digitalTextbook
     * @return
     */
    @AutoLog(value = "digital_textbook-编辑")
    @ApiOperation(value = "管理后台-编辑", notes = "管理后台-编辑")
    @RequiresPermissions("digital_textbook:digital_textbook:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    @Transactional
    public Result<String> edit(@Valid @RequestBody DigitalTextbook digitalTextbook) {
        DigitalTextbook textbook = digitalTextbookService.getOne(new QueryWrapper<DigitalTextbook>().lambda().eq(DigitalTextbook::getId, digitalTextbook.getId()));
        if (!digitalTextbook.getCode().equals(textbook.getCode())) {
            iDigitalTextbookContentService.remove(new QueryWrapper<DigitalTextbookContent>().lambda().eq(DigitalTextbookContent::getTextbookId, textbook.getId()));
            DigitalTextbookContent digitalTextbookContent = new DigitalTextbookContent();
            digitalTextbookContent.setTextbookId(digitalTextbook.getId());
            iDigitalTextbookContentService.update(digitalTextbookContent, new QueryWrapper<DigitalTextbookContent>().lambda().eq(DigitalTextbookContent::getFileHash, digitalTextbook.getCode()));
        }
        digitalTextbookService.updateById(digitalTextbook);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "digital_textbook-通过id删除")
    @ApiOperation(value = "管理后台-通过id删除", notes = "管理后台-通过id删除")
    @RequiresPermissions("digital_textbook:digital_textbook:delete")
    @PostMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        digitalTextbookService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "digital_textbook-批量删除")
    @ApiOperation(value = "管理后台-批量删除", notes = "管理后台-批量删除")
    @RequiresPermissions("digital_textbook:digital_textbook:deleteBatch")
    @PostMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.digitalTextbookService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @Value("${jeecg.path.upload}")
    private String upLoadPath;

    @Value("${jeecg.path.domain}")
    private String domain;

    @Value("${server.servlet.context-path}")
    private String contextPath;


    @PostMapping("/parsePdf")
    @Transactional
    @ApiOperation(value = "管理后台 - 上传PDF文件并解析", notes = "管理后台 - 上传PDF文件并解析")
    public Result<HashMap<String, Object>> parsePdfToImages(
            @RequestParam("file") MultipartFile pdfFile,
            @RequestParam(value = "code", defaultValue = "1") String code,
            @RequestParam(value = "dpi", defaultValue = "150") int dpi,
            @RequestParam(value = "format", defaultValue = "svg") String format) throws IOException {

        // 验证文件
        if (pdfFile.isEmpty() || !pdfFile.getOriginalFilename().toLowerCase().endsWith(".pdf")) {
            return Result.error("请上传PDF文件（格式必须为 .pdf）");
        }

        try {
            // 使用Apache Tika检测真实文件类型
            Tika tika = new Tika();
            String detectedType = tika.detect(pdfFile.getBytes());
            if (!"application/pdf".equals(detectedType)) {
                return Result.error("文件类型不合法，检测到实际类型: " + detectedType);
            }
        } catch (IOException e) {
            return Result.error("文件类型检测失败: " + e.getMessage());
        }

        // 获取PDF页数
        int totalPages = 0;
        byte[] fileBytes = pdfFile.getBytes();
        try (PDDocument document = PDDocument.load(new ByteArrayInputStream(fileBytes))) {
            // 检查PDF是否加密
            if (document.isEncrypted()) {
                return Result.error("加密PDF文件不被允许");
            }
            // 检查PDF中是否包含JavaScript
            PDDocumentCatalog catalog = document.getDocumentCatalog();
            if (catalog.getAcroForm() != null && catalog.getAcroForm().getXFA() != null) {
                return Result.error("包含XFA表单的PDF不被允许");
            }
            // 检测JavaScript
            if (containsMaliciousJavaScript(document)) {
                return Result.error("PDF包含恶意JavaScript代码,请检查后上传");
            }
            totalPages = document.getNumberOfPages();
        } catch (IOException e) {
            return Result.error("PDF加载失败: " + e.getMessage());
        }

        // 创建基础目录结构
        String basePath = upLoadPath + File.separator + "pdf_images" + File.separator + code + File.separator;
        File baseDir = new File(basePath);
        if (!baseDir.exists() && !baseDir.mkdirs()) {
            return Result.error("基础目录创建失败: " + basePath);
        }

        // 处理文件名 - 移除特殊字符
        String originalFilename = pdfFile.getOriginalFilename();
        String safeFilename = originalFilename.replaceAll("[\\\\/:*?\"<>|$【】]", "_");

        // 保存原始PDF文件
        String pdfStoragePath = basePath + "original" + File.separator;
        File pdfStorageDir = new File(pdfStoragePath);
        if (!pdfStorageDir.exists() && !pdfStorageDir.mkdirs()) {
            return Result.error("PDF存储目录创建失败: " + pdfStoragePath);
        }

        String pdfFilePath = pdfStoragePath + safeFilename;
        File pdfFileStorage = new File(pdfFilePath);

        try {
            // 确保父目录存在
            File parent = pdfFileStorage.getParentFile();
            if (!parent.exists()) {
                parent.mkdirs();
            }

            // 使用Files.copy替代transferTo，更可靠
            Files.copy(pdfFile.getInputStream(), pdfFileStorage.toPath(), StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            log.error("文件保存失败", e);
            return Result.error("PDF文件保存失败: " + e.getMessage());
        }

        // 创建图片输出目录
        String imageOutputPath = basePath;
        File imageOutputDir = new File(imageOutputPath);
        if (!imageOutputDir.exists() && !imageOutputDir.mkdirs()) {
            return Result.error("图片输出目录创建失败: " + imageOutputPath);
        }

        // 异步处理PDF转图片
        digitalTextbookService.handlePdfToImage(fileBytes, dpi, format, imageOutputDir, code);

        // 返回结果
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", code);
        resultMap.put("totalPages", totalPages);
        resultMap.put("originalPdfPath", domain + contextPath + "/sys/common/static/pdf_images/" + code + "/original/" + safeFilename);
        return Result.OK("PDF解析任务已异步提交", resultMap);
    }

    private boolean containsMaliciousJavaScript(PDDocument document) throws IOException {
        // 1. 检查文档级JavaScript（包括Catalog和AcroForm）
        PDDocumentCatalog catalog = document.getDocumentCatalog();
        if (containsJavaScript(catalog.getCOSObject())) {
            return true;
        }

        if (catalog.getAcroForm() != null && containsJavaScript(catalog.getAcroForm().getCOSObject())) {
            return true;
        }

        // 2. 检查所有页面中的注解动作
        for (PDPage page : document.getPages()) {
            // 检查页面级AA动作
            if (page.getCOSObject().containsKey(COSName.AA)) {
                return true;
            }

            // 检查所有注解
            for (PDAnnotation annotation : page.getAnnotations()) {
                COSDictionary annotDict = annotation.getCOSObject();

                // 检测/AA结构中的嵌套JavaScript
                if (containsJavaScript(annotDict)) {
                    return true;
                }
            }
        }
        return false;
    }

    // 深度检查COS字典中的JavaScript
    private boolean containsJavaScript(COSDictionary dict) {
        // 直接检查JS条目
        if (dict.containsKey(COSName.JS)) {
            return true;
        }

        // 检查AA结构中的嵌套JS
        if (dict.containsKey(COSName.AA)) {
            COSBase aa = dict.getDictionaryObject(COSName.AA);
            if (aa instanceof COSDictionary) {
                for (COSName key : ((COSDictionary) aa).keySet()) {
                    COSBase value = ((COSDictionary) aa).getDictionaryObject(key);
                    if (value instanceof COSDictionary &&
                            ((COSDictionary) value).containsKey(COSName.JS)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 查询目前解析出多少图片
     */
    @ApiOperation(value = "管理后台 - 查询目前解析出多少图片", notes = "管理后台 - 查询目前解析出多少图片")
    @RequestMapping(value = "/getImageCount", method = {RequestMethod.GET, RequestMethod.POST})
    public Result<?> getImageCount(@RequestParam(name = "code", required = true) String code) {
        long count = iDigitalTextbookContentService.count(new QueryWrapper<DigitalTextbookContent>().lambda()
                .eq(DigitalTextbookContent::getFileHash, code).orderByAsc(DigitalTextbookContent::getPageNumber));
        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
        stringObjectHashMap.put("count", count);
        return Result.OK("查询成功", stringObjectHashMap);
    }


    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "digital_textbook-通过id查询")
    @ApiOperation(value = "管理后台-通过id查询", notes = "管理后台-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<DigitalTextbook> queryById(@RequestParam(name = "id", required = true) String id) {
        DigitalTextbook digitalTextbook = digitalTextbookService.getById(id);
        if (digitalTextbook == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(digitalTextbook);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param digitalTextbook
     */
    @ApiOperation(value = "管理后台-通过id查询")
    @RequiresPermissions("digital_textbook:digital_textbook:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DigitalTextbook digitalTextbook) {
        return super.exportXls(request, digitalTextbook, DigitalTextbook.class, "digital_textbook");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @ApiOperation(value = "管理后台-通过excel导入数据")
    @RequiresPermissions("digital_textbook:digital_textbook:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DigitalTextbook.class);
    }


//    /**
//     * 我的教材
//     */
//    @ApiOperation(value = "客户端 - 我的教材", notes = "客户端 - 我的教材")
//    @RequestMapping(value = "/a/myTextbook", method = {RequestMethod.GET, RequestMethod.POST})
//    public Result<?> myTextbook(@RequestParam(name = "subjectId", defaultValue = "") String subjectId,
//                                @RequestParam(name = "stageId", defaultValue = "") String stageId ) {
//        String userId = CommonUtils.getUserIdByToken();
//        if (oConvertUtils.isEmpty(userId)) {
//            return Result.error("未找到对应数据");
//        }
//        List<UserTextbook> userTextbooks = userTextbookService.list(new QueryWrapper<UserTextbook>().lambda().eq(UserTextbook::getCreateBy, userId));
//        if (userTextbooks.size() <= 0) {
//            return Result.OK("您还未激活图书，请先激活", null);
//        }
//        List<String> digiTextIds = userTextbooks.stream().map(UserTextbook::getTextbookId).collect(Collectors.toList());
//        LambdaQueryWrapper<DigitalTextbook> queryWrapper = new QueryWrapper<DigitalTextbook>().lambda()
//                .eq(StringUtils.isNotBlank(subjectId), DigitalTextbook::getSubjectId, subjectId)
//
//                .in(DigitalTextbook::getId, digiTextIds).orderByDesc(DigitalTextbook::getCreateTime);
//        if(StringUtils.isNotBlank(stageId)){
//            SchoolStage byId = schoolStageService.getById(stageId);
//            if(byId.getPid().equals("0")){
//                List<SchoolStage> schoolStages = schoolStageService.list(new QueryWrapper<SchoolStage>().lambda()
//                        .eq(SchoolStage::getPid, stageId));
//                List<String> stageIds = schoolStages.stream().map(SchoolStage::getId).collect(Collectors.toList());
//                queryWrapper.in(DigitalTextbook::getStageId, stageIds);
//            } else {
//                queryWrapper.eq(StringUtils.isNotBlank(stageId), DigitalTextbook::getStageId, stageId);
//            }
//        }
//        List<DigitalTextbook> list = digitalTextbookService.list(queryWrapper);
//        return Result.OK(list);
//    }

    /**
     * 我的教材
     */
    @ApiOperation(value = "客户端 - 我的教材", notes = "客户端 - 我的教材")
    @RequestMapping(value = "/a/myTextbook", method = {RequestMethod.GET, RequestMethod.POST})
    public Result<?> myTextbook(@RequestParam(name = "subjectId", defaultValue = "") String subjectId,
                                @RequestParam(name = "stageId", defaultValue = "") String stageId) {
        String userId = CommonUtils.getUserIdByToken();
        if (oConvertUtils.isEmpty(userId)) {
            return Result.error("未找到对应数据");
        }
        List<UserTextbook> userTextbooks = userTextbookService.list(new QueryWrapper<UserTextbook>().lambda().eq(UserTextbook::getCreateBy, userId));
        List<String> digiTextIds = userTextbooks.stream().map(UserTextbook::getTextbookId).collect(Collectors.toList());
        LambdaQueryWrapper<DigitalTextbook> queryWrapper = new QueryWrapper<DigitalTextbook>().lambda()
                .eq(StringUtils.isNotBlank(subjectId), DigitalTextbook::getSubjectId, subjectId)
                .orderByAsc(DigitalTextbook::getSort);
        if (StringUtils.isNotBlank(stageId)) {
            SchoolStage byId = schoolStageService.getById(stageId);
            if (byId.getPid().equals("0")) {
                List<SchoolStage> schoolStages = schoolStageService.list(new QueryWrapper<SchoolStage>().lambda()
                        .eq(SchoolStage::getPid, stageId));
                List<String> stageIds = schoolStages.stream().map(SchoolStage::getId).collect(Collectors.toList());
                queryWrapper.in(DigitalTextbook::getStageId, stageIds);
            } else {
                queryWrapper.eq(StringUtils.isNotBlank(stageId), DigitalTextbook::getStageId, stageId);
            }
        }
        List<DigitalTextbook> list = digitalTextbookService.list(queryWrapper);
        for (DigitalTextbook item : list) {
            item.setIsActive(digiTextIds.contains(item.getId()));
            LambdaQueryWrapper<SchoolStage> schoolStageLambdaQueryWrapper = new QueryWrapper<SchoolStage>().lambda()
                    .eq(SchoolStage::getId, item.getStageId());
            SchoolStage schoolStage = schoolStageService.getOne(schoolStageLambdaQueryWrapper);
            item.setTitle(schoolStage.getName());
        }
        return Result.OK(list);
    }

    @ApiOperation(value = "客户端 - 教材详情", notes = "客户端 - 教材详情")
    @RequestMapping(value = "/a/textbookDetail", method = {RequestMethod.GET, RequestMethod.POST})
    public Result<?> textbookDetail(@RequestParam(name = "id", defaultValue = "") String id) {
        String userId = CommonUtils.getUserIdByToken();
        UserTextbook userTextbooks = userTextbookService.getOne(new QueryWrapper<UserTextbook>().lambda().eq(UserTextbook::getCreateBy, userId).eq(UserTextbook::getTextbookId, id));
        if (userTextbooks == null) {
            return Result.OK("您还未激活图书，请先激活", null);
        }

        DigitalTextbook digitalTextbook = digitalTextbookService.getById(id);
        if (digitalTextbook == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(digitalTextbook);
    }

    @Transactional
    @PostMapping("/a/addTextbook")
    @ApiOperation(value = "客户端 - 添加教材教材/激活教材", notes = "客户端 - 添加教材教材/激活教材")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "激活码", required = true, paramType = "query")
    })
    public Result<?> addTextbook(@Valid @RequestBody AddTextBookDto addTextBookDto) {
        try {
            ActivationCode activationCode = activationCodeService.findAndLockByCode(addTextBookDto.getCode());

            if (activationCode == null) {
                return Result.error("激活码无效、已被使用或已过期");
            }

            ActivationCodeBatch batch = activationCodeBatchService.getById(activationCode.getBatchId());
            if (batch == null) {
                return Result.error("批次无效");
            }
            if (batch.getStatus() != 1) {
                return Result.error("批次已停用");
            }

            String userId = CommonUtils.getUserIdByToken();

            // 验证是否已激活过该教材（已使用和过期验证已在SQL查询中完成）
            boolean alreadyActivated = userTextbookService.exists(
                    new QueryWrapper<UserTextbook>()
                            .lambda()
                            .eq(UserTextbook::getUserId, userId)
                            .eq(UserTextbook::getTextbookId, activationCode.getDigitextId())
            );
            if (alreadyActivated) {
                return Result.error("您已激活过该教材");
            }

            // 更新激活码状态
            activationCode.setIsUsed(1);
            activationCode.setUsedBy(userId);
            activationCode.setUsedTime(new Date());
            activationCodeService.updateById(activationCode);

            // 添加用户与教材关联
            UserTextbook userTextbook = new UserTextbook();
            userTextbook.setUserId(userId);
            userTextbook.setTextbookId(activationCode.getDigitextId());
            userTextbook.setIsActivated(1);
            userTextbook.setActivatedAt(new Date());
            userTextbook.setCreateTime(new Date());
            userTextbook.setCreateBy(userId);
            userTextbookService.save(userTextbook);

            return Result.ok("激活成功！");
        } catch (Exception e) {
            log.error("激活教材失败，code: {}", addTextBookDto.getCode(), e);
            return Result.error("添加失败，请稍后重试或联系管理员");
        }
    }
}
