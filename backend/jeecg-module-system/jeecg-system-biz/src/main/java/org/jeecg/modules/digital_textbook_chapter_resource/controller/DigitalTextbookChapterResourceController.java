package org.jeecg.modules.digital_textbook_chapter_resource.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.digital_textbook_chapter_resource.entity.DigitalTextbookChapterResource;
import org.jeecg.modules.digital_textbook_chapter_resource.entity.DigitalTextbookChapterResourceBatchDto;
import org.jeecg.modules.digital_textbook_chapter_resource.service.IDigitalTextbookChapterResourceService;
import org.jeecg.modules.digital_textbook_question.entity.DigitalTextbookQuestion;
import org.jeecg.modules.digital_textbook_question.entity.DigitalTextbookQuestionDto;
import org.jeecg.modules.digital_textbook_question.service.IDigitalTextbookQuestionService;
import org.jeecg.modules.resource_file.entity.ResourceFile;
import org.jeecg.modules.resource_file.service.IResourceFileService;
import org.jeecg.modules.user_textbook.entity.UserTextbook;
import org.jeecg.modules.user_textbook.service.IUserTextbookService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: digital_textbook_chapter_resource
 * @Author: jeecg-boot
 * @Date: 2025-05-17
 * @Version: V1.0
 */
@Api(tags = "资源列表管理")
@RestController
@RequestMapping("/resource")
@Slf4j
public class DigitalTextbookChapterResourceController extends JeecgController<DigitalTextbookChapterResource, IDigitalTextbookChapterResourceService> {
    @Autowired
    private IDigitalTextbookChapterResourceService digitalTextbookChapterResourceService;

    @Autowired
    private IResourceFileService resourceFileService;

    @Autowired
    private IDigitalTextbookQuestionService digitalTextbookQuestionService;

    /**
     * 分页列表查询
     *
     * @param digitalTextbookChapterResource
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "digital_textbook_chapter_resource-分页列表查询")
    @ApiOperation(value = "管理后台-分页列表查询", notes = "管理后台-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<DigitalTextbookChapterResource>> queryPageList(DigitalTextbookChapterResource digitalTextbookChapterResource,
                                                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                                       @RequestParam(name = "keyword", defaultValue = "") String keyword,
                                                                       HttpServletRequest req) {
        QueryWrapper<DigitalTextbookChapterResource> queryWrapper = QueryGenerator.initQueryWrapper(digitalTextbookChapterResource, req.getParameterMap());
        queryWrapper.lambda().like(StringUtils.isNotBlank(keyword), DigitalTextbookChapterResource::getResourceName, keyword);
        queryWrapper.lambda().orderByAsc(DigitalTextbookChapterResource::getSort);
        Page<DigitalTextbookChapterResource> page = new Page<DigitalTextbookChapterResource>(pageNo, pageSize);
        IPage<DigitalTextbookChapterResource> pageList = digitalTextbookChapterResourceService.page(page, queryWrapper);
        pageList.getRecords().forEach(item -> {
            if (StringUtils.isNotBlank(item.getResourceId())) {
                if ("resource".equals(item.getResourceType())) {
                    item.setResourceList(resourceFileService.getOne(new QueryWrapper<ResourceFile>().lambda().eq(ResourceFile::getId, item.getResourceId())));
                } else {
                    item.setResourceList(digitalTextbookQuestionService.getOne(new QueryWrapper<DigitalTextbookQuestion>().lambda().eq(DigitalTextbookQuestion::getId, item.getResourceId())));
                }
            }
        });
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param digitalTextbookChapterResource
     * @return
     */
    @AutoLog(value = "resource-添加")
    @ApiOperation(value = "管理后台-添加", notes = "管理后台-添加")
    @RequiresPermissions("resource:resource:add")
    @PostMapping(value = "/add")
    public Result<String> add(@Valid @RequestBody DigitalTextbookChapterResource digitalTextbookChapterResource) {
        digitalTextbookChapterResourceService.save(digitalTextbookChapterResource);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     * @param digitalTextbookChapterResource
     * @return
     */
    @AutoLog(value = "resource-编辑")
    @ApiOperation(value = "管理后台-编辑", notes = "管理后台-编辑")
    @RequiresPermissions("resource:resource:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@Valid @RequestBody DigitalTextbookChapterResource digitalTextbookChapterResource) {
        digitalTextbookChapterResourceService.updateById(digitalTextbookChapterResource);
        return Result.OK("编辑成功!");
    }

    /**
     * 批量编辑
     */
    @AutoLog(value = "resource-批量编辑")
    @ApiOperation(value = "管理后台-批量编辑", notes = "管理后台-批量编辑")
    @RequiresPermissions("resource:resource:edit")
    @PostMapping(value = "/editBatch")
    @Transactional
    public Result<String> editBatch(@RequestBody DigitalTextbookChapterResourceBatchDto digitalTextbookChapterResourceBatchDto) {
        try {
            // 1. 参数校验
            if (digitalTextbookChapterResourceBatchDto == null) {
                return Result.error("参数不能为空");
            }
            if (StringUtils.isBlank(digitalTextbookChapterResourceBatchDto.getTextbookId())
                    || StringUtils.isBlank(digitalTextbookChapterResourceBatchDto.getChapterId())) {
                return Result.error("教材ID和章节ID不能为空");
            }
            if (CollectionUtils.isEmpty(digitalTextbookChapterResourceBatchDto.getResourceList())) {
                return Result.error("资源列表不能为空");
            }

            // 2. 查询现有关联关系
            List<DigitalTextbookChapterResource> existingRelations = digitalTextbookChapterResourceService.list(
                    new QueryWrapper<DigitalTextbookChapterResource>().lambda()
                            .eq(DigitalTextbookChapterResource::getTextbookId, digitalTextbookChapterResourceBatchDto.getTextbookId())
                            .eq(DigitalTextbookChapterResource::getChapterId, digitalTextbookChapterResourceBatchDto.getChapterId())
                            .eq(DigitalTextbookChapterResource::getResourceType,  "question")
            );

            // 3. 删除旧数据（带事务保护）
            if (!CollectionUtils.isEmpty(existingRelations)) {
                List<String> resourceIds = existingRelations.stream()
                        .map(DigitalTextbookChapterResource::getResourceId)
                        .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(resourceIds)) {
                    // 批量删除资源（带异常捕获）
                    try {
                        digitalTextbookQuestionService.remove(new QueryWrapper<DigitalTextbookQuestion>().lambda()
                                .eq(DigitalTextbookQuestion::getTextbookIdId,  digitalTextbookChapterResourceBatchDto.getTextbookId())
                                .eq(DigitalTextbookQuestion::getChapterId,  digitalTextbookChapterResourceBatchDto.getChapterId()));
                    } catch (Exception e) {
                        log.error("删除资源失败", e);
                        throw new RuntimeException("删除资源数据失败", e);
                    }
                }

                // 删除关联关系
                try {
                    digitalTextbookChapterResourceService.remove(
                            new QueryWrapper<DigitalTextbookChapterResource>().lambda()
                                    .eq(DigitalTextbookChapterResource::getTextbookId, digitalTextbookChapterResourceBatchDto.getTextbookId())
                                    .eq(DigitalTextbookChapterResource::getChapterId, digitalTextbookChapterResourceBatchDto.getChapterId())
                                    .eq(DigitalTextbookChapterResource::getResourceType,digitalTextbookChapterResourceBatchDto.getResourceType())
                    );
                } catch (Exception e) {
                    log.error("删除关联关系失败", e);
                    throw new RuntimeException("删除关联关系失败", e);
                }
            }

            // 4. 保存新数据（并行流改为顺序处理+批处理）
            List<DigitalTextbookChapterResource> savedRelations = new ArrayList<>();
            for (DigitalTextbookQuestionDto item : digitalTextbookChapterResourceBatchDto.getResourceList()) {
                try {
                    DigitalTextbookQuestion digitalTextbookQuestion = new DigitalTextbookQuestion();
                    BeanUtils.copyProperties(item, digitalTextbookQuestion);
                    digitalTextbookQuestion.setTextbookIdId(digitalTextbookChapterResourceBatchDto.getTextbookId());
                    digitalTextbookQuestion.setChapterId(digitalTextbookChapterResourceBatchDto.getChapterId());
                    digitalTextbookQuestion.setQuestion(StringEscapeUtils.escapeHtml(digitalTextbookQuestion.getQuestion()));
                    digitalTextbookQuestion.setAnswerDesc(StringEscapeUtils.escapeHtml(digitalTextbookQuestion.getAnswerDesc()));
                    // 保存资源
                    if (!digitalTextbookQuestionService.save(digitalTextbookQuestion)) {
                        log.error("保存资源失败: {}", digitalTextbookQuestion);
                        throw new RuntimeException("保存资源失败");
                    }

                    // 保存关联关系
                    DigitalTextbookChapterResource relation = new DigitalTextbookChapterResource();
                    relation.setTextbookId(digitalTextbookChapterResourceBatchDto.getTextbookId());
                    relation.setChapterId(digitalTextbookChapterResourceBatchDto.getChapterId());
                    relation.setResourceType(digitalTextbookChapterResourceBatchDto.getResourceType());
                    relation.setResourceId(digitalTextbookQuestion.getId());

                    if (!digitalTextbookChapterResourceService.save(relation)) {
                        log.error("保存关联关系失败: {}", relation);
                        throw new RuntimeException("保存关联关系失败");
                    }
                    savedRelations.add(relation);

                } catch (Exception e) {
                    if (!savedRelations.isEmpty()) {
                        digitalTextbookChapterResourceService.removeByIds(
                                savedRelations.stream().map(DigitalTextbookChapterResource::getId).collect(Collectors.toList())
                        );
                    }
                    throw new RuntimeException("批量保存过程中发生错误", e);
                }
            }

            return Result.OK("批量编辑成功！");

        } catch (Exception e) {
            log.error("批量编辑失败", e);
            return Result.error("批量编辑失败: " + e.getMessage());
        }
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "resource-通过id删除")
    @ApiOperation(value = "管理后台-通过id删除", notes = "管理后台-通过id删除")
    @RequiresPermissions("resource:resource:delete")
    @PostMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        digitalTextbookChapterResourceService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "resource-批量删除")
    @ApiOperation(value = "管理后台-批量删除", notes = "管理后台-批量删除")
    @RequiresPermissions("resource:resource:deleteBatch")
    @PostMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.digitalTextbookChapterResourceService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "resource-通过id查询")
    @ApiOperation(value = "管理后台-通过id查询", notes = "管理后台-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<DigitalTextbookChapterResource> queryById(@RequestParam(name = "id", required = true) String id) {
        DigitalTextbookChapterResource digitalTextbookChapterResource = digitalTextbookChapterResourceService.getById(id);
        if (digitalTextbookChapterResource == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(digitalTextbookChapterResource);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param digitalTextbookChapterResource
     */
    @RequiresPermissions("resource:resource:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DigitalTextbookChapterResource digitalTextbookChapterResource) {
        return super.exportXls(request, digitalTextbookChapterResource, DigitalTextbookChapterResource.class, "resource");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("resource:resource:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DigitalTextbookChapterResource.class);
    }


    @Autowired
    private IUserTextbookService userTextbookService;

    /**
     * 查询教材章节资源
     */
    @ApiOperation(value = "客户端 - 查询教材章节资源")
    @PostMapping(value = "/a/queryResource")
    public Result<List<DigitalTextbookChapterResource>> queryResource(@Valid @RequestBody DigitalTextbookChapterResource digitalTextbookChapterResource) {
        if (StringUtils.isBlank(digitalTextbookChapterResource.getTextbookId()) || StringUtils.isBlank(digitalTextbookChapterResource.getChapterId())) {
            return Result.error("参数错误");
        }

        String userId = CommonUtils.getUserIdByToken();
        UserTextbook userTextbooks = userTextbookService.getOne(new QueryWrapper<UserTextbook>().lambda().eq(UserTextbook::getUserId, userId).eq(UserTextbook::getTextbookId, digitalTextbookChapterResource.getTextbookId()));
        if (userTextbooks == null) {
            return Result.OK("您还未激活图书，请先激活", null);
        }

        List<DigitalTextbookChapterResource> list = digitalTextbookChapterResourceService.list(new QueryWrapper<DigitalTextbookChapterResource>().lambda()
                .eq(DigitalTextbookChapterResource::getTextbookId, digitalTextbookChapterResource.getTextbookId())
                .eq(DigitalTextbookChapterResource::getChapterId, digitalTextbookChapterResource.getChapterId())
                .eq(DigitalTextbookChapterResource::getResourceType, digitalTextbookChapterResource.getResourceType())
                .orderByAsc(DigitalTextbookChapterResource::getSort));

        // 如果没有记录，直接返回空列表
        if (CollectionUtils.isEmpty(list)) {
            return Result.OK(Collections.emptyList());
        }

        // 提取 resourceId
        List<String> resourceIds = list.stream()
                .map(DigitalTextbookChapterResource::getResourceId)
                .collect(Collectors.toList());

        // 根据类型批量查询资源详情
        Map<String, Object> resourceMap = new HashMap<>();
        String resourceType = digitalTextbookChapterResource.getResourceType();

        if ("resource".equals(resourceType)) {
            List<ResourceFile> resourceFiles = resourceFileService.listByIds(resourceIds);
            resourceMap = resourceFiles.stream().collect(Collectors.toMap(ResourceFile::getId, v -> (Object) v));
        } else if ("question".equals(resourceType)) {
            List<DigitalTextbookQuestion> questions = digitalTextbookQuestionService.listByIds(resourceIds);
            questions.forEach(q -> {
                q.setQuestion(StringEscapeUtils.unescapeHtml(q.getQuestion()));
                q.setAnswerDesc(StringEscapeUtils.unescapeHtml(q.getAnswerDesc()));
            });
            resourceMap = questions.stream().collect(Collectors.toMap(DigitalTextbookQuestion::getId, q -> (Object) q));
        }
        // 组装结果
        for (DigitalTextbookChapterResource res : list) {
            res.setResourceList(resourceMap.get(res.getResourceId()));
        }
        return Result.OK(list);
    }
}
