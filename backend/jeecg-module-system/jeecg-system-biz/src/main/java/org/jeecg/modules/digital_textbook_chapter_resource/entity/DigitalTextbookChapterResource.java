package org.jeecg.modules.digital_textbook_chapter_resource.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description: digital_textbook_chapter_resource
 * @Author: jeecg-boot
 * @Date:   2025-05-17
 * @Version: V1.0
 */
@Data
@TableName("digital_textbook_chapter_resource")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="digital_textbook_chapter_resource对象", description="digital_textbook_chapter_resource")
public class DigitalTextbookChapterResource implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
	/**教材id*/
    @NotEmpty(message = "教材id不能为空")
	@Excel(name = "教材id", width = 15)
    @ApiModelProperty(value = "教材id")
    private String textbookId;
	/**章节id*/
    @NotEmpty(message = "章节id不能为空")
	@Excel(name = "章节id", width = 15)
    @ApiModelProperty(value = "章节id")
    private String chapterId;
	/**资源类型*/
    @NotEmpty(message = "资源类型不能为空")
	@Excel(name = "资源类型", width = 15)
    @ApiModelProperty(value = "资源类型")
    private String resourceType;
	/**资源id*/
	@Excel(name = "资源id", width = 15)
    @ApiModelProperty(value = "资源id")
    private String resourceId;

    @Excel(name = "资源名称", width = 15)
    @ApiModelProperty(value = "资源名称")
    private String resourceName;

	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer sort;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @TableField(exist = false)
    private Object resourceList;
}
