package org.jeecg.modules.digital_textbook_chapter_resource.service.impl;

import org.jeecg.modules.digital_textbook_chapter_resource.entity.DigitalTextbookChapterResource;
import org.jeecg.modules.digital_textbook_chapter_resource.mapper.DigitalTextbookChapterResourceMapper;
import org.jeecg.modules.digital_textbook_chapter_resource.service.IDigitalTextbookChapterResourceService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: digital_textbook_chapter_resource
 * @Author: jeecg-boot
 * @Date:   2025-05-17
 * @Version: V1.0
 */
@Service
public class DigitalTextbookChapterResourceServiceImpl extends ServiceImpl<DigitalTextbookChapterResourceMapper, DigitalTextbookChapterResource> implements IDigitalTextbookChapterResourceService {

}
