import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '教材id',
    align:"center",
    dataIndex: 'textbookId'
   },
   {
    title: '章节id',
    align:"center",
    dataIndex: 'chapterId'
   },
   {
    title: '资源类型',
    align:"center",
    dataIndex: 'resourceType'
   },
   {
    title: '资源id',
    align:"center",
    dataIndex: 'resourceId'
   },
   {
    title: '排序',
    align:"center",
    dataIndex: 'sort'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '教材id',
    field: 'textbookId',
    component: 'Input',
  },
  {
    label: '章节id',
    field: 'chapterId',
    component: 'Input',
  },
  {
    label: '资源类型',
    field: 'resourceType',
    component: 'Input',
  },
  {
    label: '资源id',
    field: 'resourceId',
    component: 'Input',
  },
  {
    label: '排序',
    field: 'sort',
    component: 'InputNumber',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  textbookId: {title: '教材id',order: 0,view: 'text', type: 'string',},
  chapterId: {title: '章节id',order: 1,view: 'text', type: 'string',},
  resourceType: {title: '资源类型',order: 2,view: 'text', type: 'string',},
  resourceId: {title: '资源id',order: 3,view: 'text', type: 'string',},
  sort: {title: '排序',order: 4,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}