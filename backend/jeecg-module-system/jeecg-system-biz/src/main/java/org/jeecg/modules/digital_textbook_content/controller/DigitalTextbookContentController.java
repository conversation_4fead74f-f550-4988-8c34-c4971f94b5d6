package org.jeecg.modules.digital_textbook_content.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.digital_textbook_content.entity.DigitalTextbookContent;
import org.jeecg.modules.digital_textbook_content.service.IDigitalTextbookContentService;
import org.jeecg.modules.user_textbook.entity.UserTextbook;
import org.jeecg.modules.user_textbook.service.IUserTextbookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: digital_textbook_content
 * @Author: jeecg-boot
 * @Date: 2025-05-17
 * @Version: V1.0
 */
@Api(tags = "数字化教材内容管理 - pdf剪切图片内容")
@RestController
@RequestMapping("/digital_textbook_content/digitalTextbookContent")
@Slf4j
public class DigitalTextbookContentController extends JeecgController<DigitalTextbookContent, IDigitalTextbookContentService> {
    @Autowired
    private IDigitalTextbookContentService digitalTextbookContentService;

    @Autowired
    private IUserTextbookService userTextbookService;

    /**
     * 分页列表查询
     *
     * @param digitalTextbookContent
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "digital_textbook_content-分页列表查询")
    @ApiOperation(value = "管理后台-分页列表查询", notes = "管理后台-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<DigitalTextbookContent>> queryPageList(DigitalTextbookContent digitalTextbookContent,
                                                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                               HttpServletRequest req) {
        QueryWrapper<DigitalTextbookContent> queryWrapper = QueryGenerator.initQueryWrapper(digitalTextbookContent, req.getParameterMap());
        long count = digitalTextbookContentService.count(queryWrapper);
        pageSize = Math.toIntExact(count);
        queryWrapper.lambda().orderByAsc(DigitalTextbookContent::getPageNumber);
        Page<DigitalTextbookContent> page = new Page<DigitalTextbookContent>(pageNo, pageSize);
        IPage<DigitalTextbookContent> pageList = digitalTextbookContentService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param digitalTextbookContent
     * @return
     */
    @AutoLog(value = "digital_textbook_content-添加")
    @ApiOperation(value = "管理后台-添加", notes = "管理后台-添加")
    @RequiresPermissions("digital_textbook_content:digital_textbook_content:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody DigitalTextbookContent digitalTextbookContent) {
        digitalTextbookContentService.save(digitalTextbookContent);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param digitalTextbookContentDto
     * @return
     */
    @AutoLog(value = "digital_textbook_content-编辑")
    @ApiOperation(value = "管理后台-编辑", notes = "管理后台-编辑")
    @RequiresPermissions("digital_textbook_content:digital_textbook_content:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody DigitalTextbookContent digitalTextbookContentDto) {
        digitalTextbookContentService.updateById(digitalTextbookContentDto);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "digital_textbook_content-通过id删除")
    @ApiOperation(value = "管理后台-通过id删除", notes = "管理后台-通过id删除")
    @RequiresPermissions("digital_textbook_content:digital_textbook_content:delete")
    @PostMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        digitalTextbookContentService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "digital_textbook_content-批量删除")
    @ApiOperation(value = "管理后台-批量删除", notes = "管理后台-批量删除")
    @RequiresPermissions("digital_textbook_content:digital_textbook_content:deleteBatch")
    @PostMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.digitalTextbookContentService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "digital_textbook_content-通过id查询")
    @ApiOperation(value = "管理后台-通过id查询", notes = "管理后台-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<DigitalTextbookContent> queryById(@RequestParam(name = "id", required = true) String id) {
        DigitalTextbookContent digitalTextbookContent = digitalTextbookContentService.getById(id);
        if (digitalTextbookContent == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(digitalTextbookContent);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param digitalTextbookContent
     */
    @RequiresPermissions("digital_textbook_content:digital_textbook_content:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DigitalTextbookContent digitalTextbookContent) {
        return super.exportXls(request, digitalTextbookContent, DigitalTextbookContent.class, "digital_textbook_content");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("digital_textbook_content:digital_textbook_content:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DigitalTextbookContent.class);
    }

    /**
     * 根据图书查询pdf内容
     */
    @ApiOperation(value = "客户端 - 数字化教材内容", notes = "客户端 - 数字化教材内容")
    @GetMapping(value = "/a/getPdfContent")
    public Result<IPage<DigitalTextbookContent>> getPdfContent(@RequestParam(name = "textbookId") String textbookId,
                                                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        String userId = CommonUtils.getUserIdByToken();
        boolean exists = userTextbookService.exists(new QueryWrapper<UserTextbook>().lambda()
                .eq(UserTextbook::getTextbookId, textbookId)
                .eq(UserTextbook::getUserId, userId));
        if (!exists) {
            return Result.error("您未激活该图书");
        }

        // 先查询总记录数
        LambdaQueryWrapper<DigitalTextbookContent> countQueryWrapper = new QueryWrapper<DigitalTextbookContent>().lambda()
                .eq(DigitalTextbookContent::getTextbookId, textbookId);
        long count = digitalTextbookContentService.count(countQueryWrapper);

        // 设置分页参数
        pageSize = Math.toIntExact(count);
        Page<DigitalTextbookContent> page = new Page<DigitalTextbookContent>(pageNo, pageSize);

        // 再查询分页数据，并在分页查询中进行排序
        LambdaQueryWrapper<DigitalTextbookContent> queryWrapper = new QueryWrapper<DigitalTextbookContent>().lambda()
                .eq(DigitalTextbookContent::getTextbookId, textbookId)
                .orderByAsc(DigitalTextbookContent::getPageNumber);
        IPage<DigitalTextbookContent> pageList = digitalTextbookContentService.page(page, queryWrapper);

        return Result.OK(pageList);
    }

}
