package org.jeecg.modules.digital_textbook_question.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.digital_textbook_question.entity.DigitalTextbookQuestion;
import org.jeecg.modules.digital_textbook_question.service.IDigitalTextbookQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: digital_textbook_question
 * @Author: jeecg-boot
 * @Date:   2025-05-17
 * @Version: V1.0
 */
@Api(tags="数字化教材题目管理")
@RestController
@RequestMapping("/digital_textbook_question/digitalTextbookQuestion")
@Slf4j
public class DigitalTextbookQuestionController extends JeecgController<DigitalTextbookQuestion, IDigitalTextbookQuestionService> {
	@Autowired
	private IDigitalTextbookQuestionService digitalTextbookQuestionService;
	
	/**
	 * 分页列表查询
	 *
	 * @param digitalTextbookQuestion
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "digital_textbook_question-分页列表查询")
	@ApiOperation(value="管理后台-分页列表查询", notes="管理后台-分页列表查询")
	@GetMapping(value = "/list")
	public Result<List<DigitalTextbookQuestion>> queryPageList(DigitalTextbookQuestion digitalTextbookQuestion,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<DigitalTextbookQuestion> queryWrapper = QueryGenerator.initQueryWrapper(digitalTextbookQuestion, req.getParameterMap());
		List<DigitalTextbookQuestion> pageList = digitalTextbookQuestionService.list(queryWrapper);
		pageList.forEach(item -> {
			item.setQuestion(StringEscapeUtils.unescapeHtml(item.getQuestion()));
			item.setAnswerDesc(StringEscapeUtils.unescapeHtml(item.getAnswerDesc()));
		});
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param digitalTextbookQuestion
	 * @return
	 */
	@AutoLog(value = "digital_textbook_question-添加")
	@ApiOperation(value="管理后台-添加", notes="管理后台-添加")
	@RequiresPermissions("digital_textbook_question:digital_textbook_question:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody DigitalTextbookQuestion digitalTextbookQuestion) {
		digitalTextbookQuestionService.save(digitalTextbookQuestion);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param digitalTextbookQuestion
	 * @return
	 */
	@AutoLog(value = "digital_textbook_question-编辑")
	@ApiOperation(value="管理后台-编辑", notes="管理后台-编辑")
	@RequiresPermissions("digital_textbook_question:digital_textbook_question:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody DigitalTextbookQuestion digitalTextbookQuestion) {
		digitalTextbookQuestionService.updateById(digitalTextbookQuestion);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "digital_textbook_question-通过id删除")
	@ApiOperation(value="管理后台-通过id删除", notes="管理后台-通过id删除")
	@RequiresPermissions("digital_textbook_question:digital_textbook_question:delete")
	@PostMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		digitalTextbookQuestionService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "digital_textbook_question-批量删除")
	@ApiOperation(value="管理后台-批量删除", notes="管理后台-批量删除")
	@RequiresPermissions("digital_textbook_question:digital_textbook_question:deleteBatch")
	@PostMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.digitalTextbookQuestionService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "digital_textbook_question-通过id查询")
	@ApiOperation(value="管理后台-通过id查询", notes="管理后台-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<DigitalTextbookQuestion> queryById(@RequestParam(name="id",required=true) String id) {
		DigitalTextbookQuestion digitalTextbookQuestion = digitalTextbookQuestionService.getById(id);
		if(digitalTextbookQuestion==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(digitalTextbookQuestion);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param digitalTextbookQuestion
    */
    @RequiresPermissions("digital_textbook_question:digital_textbook_question:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DigitalTextbookQuestion digitalTextbookQuestion) {
        return super.exportXls(request, digitalTextbookQuestion, DigitalTextbookQuestion.class, "digital_textbook_question");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("digital_textbook_question:digital_textbook_question:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DigitalTextbookQuestion.class);
    }
}
