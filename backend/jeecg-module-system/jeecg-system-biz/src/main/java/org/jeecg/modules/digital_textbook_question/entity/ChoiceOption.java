package org.jeecg.modules.digital_textbook_question.entity;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="选项列表", description="选项列表")
public class ChoiceOption {
    /** 选项字母，例如 A、B、C、D */
    private String option;

    /** 选项对应的答案文本，例如 "答案一" */
    private String optionAnswer;
}
