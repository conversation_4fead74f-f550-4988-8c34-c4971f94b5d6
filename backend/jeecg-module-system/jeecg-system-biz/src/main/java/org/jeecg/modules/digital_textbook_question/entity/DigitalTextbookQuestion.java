package org.jeecg.modules.digital_textbook_question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonRawValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.JdbcType;
import org.jeecg.common.handler.JsonTypeHandler;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description: digital_textbook_question
 * @Author: jeecg-boot
 * @Date:   2025-05-17
 * @Version: V1.0
 */
@Data
@TableName("digital_textbook_question")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="digital_textbook_question对象", description="digital_textbook_question")
public class DigitalTextbookQuestion implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
	/**uuid*/
	@Excel(name = "uuid", width = 15)
    @ApiModelProperty(value = "uuid")
    private String uuid;
	/**云教材id*/
    @NotEmpty(message = "云教材id不能为空")
	@Excel(name = "云教材id", width = 15)
    @ApiModelProperty(value = "云教材id")
    private String textbookIdId;
	/**章id*/
    @NotEmpty(message = "章id不能为空")
	@Excel(name = "章id", width = 15)
    @ApiModelProperty(value = "章id")
    private String chapterId;
	/**问题*/
    @NotEmpty(message = "问题不能为空")
	@Excel(name = "问题", width = 15)
    @ApiModelProperty(value = "问题")
    private String question;
	/**类别 1单选题 2多选题 3判断题 4连线题 5排序题 6填空题 7简答题*/
	@Excel(name = "类别 1单选题 2多选题 3判断题 4连线题 5排序题 6填空题 7简答题", width = 15)
    @ApiModelProperty(value = "类别 1单选题 2多选题 3判断题 4连线题 5排序题 6填空题 7简答题")
    private Integer type;
	/**类型标识*/
	@Excel(name = "类型标识", width = 15)
    @ApiModelProperty(value = "类型标识")
    private String identifier;
	/**选项（用于单选/多选）*/
	@Excel(name = "选项（用于单选/多选）", width = 15)
    @ApiModelProperty(value = "选项（用于单选/多选）")
    @TableField(typeHandler = JsonTypeHandler.class, jdbcType = JdbcType.VARCHAR)
    @JsonRawValue
    private Object choices;
	/**正确答案*/
	@Excel(name = "正确答案", width = 15)
    @ApiModelProperty(value = "正确答案")
    @TableField(typeHandler = JsonTypeHandler.class, jdbcType = JdbcType.VARCHAR)
    @JsonRawValue
    private Object selected;
	/**创建时间*/
	@Excel(name = "创建时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createAt;
	/**修改时间*/
	@Excel(name = "修改时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateAt;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer sort;
	/**左选项*/
	@Excel(name = "左选项", width = 15)
    @ApiModelProperty(value = "左选项")
    @TableField(typeHandler = JsonTypeHandler.class, jdbcType = JdbcType.VARCHAR)
    @JsonRawValue
    private Object leftOptions;
	/**右选项*/
	@Excel(name = "右选项", width = 15)
    @ApiModelProperty(value = "右选项")
    @TableField(typeHandler = JsonTypeHandler.class, jdbcType = JdbcType.VARCHAR)
    @JsonRawValue
    private Object rightOptions;
	/**连线题*/
	@Excel(name = "连线题", width = 15)
    @ApiModelProperty(value = "连线题")
    @TableField(typeHandler = JsonTypeHandler.class, jdbcType = JdbcType.VARCHAR)
    @JsonRawValue
    private Object matchData;
	/**简答题小题目*/
	@Excel(name = "简答题小题目", width = 15)
    @ApiModelProperty(value = "简答题小题目")
    @TableField(typeHandler = JsonTypeHandler.class, jdbcType = JdbcType.VARCHAR)
    @JsonRawValue
    private Object questionShort;
	/**添加批次id(前端使用)*/
	@Excel(name = "添加批次id(前端使用)", width = 15)
    @ApiModelProperty(value = "添加批次id(前端使用)")
    private String batchId;
	/**extraFields*/
	@Excel(name = "extraFields", width = 15)
    @ApiModelProperty(value = "extraFields")
    @TableField(typeHandler = JsonTypeHandler.class, jdbcType = JdbcType.VARCHAR)
    @JsonRawValue
    private String extraFields;
	/**answerDesc*/
	@Excel(name = "answerDesc", width = 15)
    @ApiModelProperty(value = "answerDesc")
    private String answerDesc;
	/**createBy*/
    @ApiModelProperty(value = "createBy")
    private String createBy;
	/**updateBy*/
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

}
