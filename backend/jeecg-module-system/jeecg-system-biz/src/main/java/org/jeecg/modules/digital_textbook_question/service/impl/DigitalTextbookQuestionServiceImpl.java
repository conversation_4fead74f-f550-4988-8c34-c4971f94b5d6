package org.jeecg.modules.digital_textbook_question.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.digital_textbook_question.entity.DigitalTextbookQuestion;
import org.jeecg.modules.digital_textbook_question.mapper.DigitalTextbookQuestionMapper;
import org.jeecg.modules.digital_textbook_question.service.IDigitalTextbookQuestionService;
import org.springframework.stereotype.Service;

/**
 * @Description: digital_textbook_question
 * @Author: jeecg-boot
 * @Date:   2025-05-17
 * @Version: V1.0
 */
@Service
public class DigitalTextbookQuestionServiceImpl extends ServiceImpl<DigitalTextbookQuestionMapper, DigitalTextbookQuestion> implements IDigitalTextbookQuestionService {




}
