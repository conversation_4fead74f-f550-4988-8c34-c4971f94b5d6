<template>
    <view>
        <!--标题和返回-->
		<cu-custom :bgColor="NavBarColor" isBack :backRouterName="backRouteName">
			<block slot="backText">返回</block>
			<block slot="content">digital_textbook_question</block>
		</cu-custom>
		 <!--表单区域-->
		<view>
			<form>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">uuid：</text></view>
                  <input  placeholder="请输入uuid" v-model="model.uuid"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">云教材id：</text></view>
                  <input  placeholder="请输入云教材id" v-model="model.textbookIdId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">章id：</text></view>
                  <input  placeholder="请输入章id" v-model="model.chapterId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">问题：</text></view>
                  <input  placeholder="请输入问题" v-model="model.question"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">类别 1单选题 2多选题 3判断题 4连线题 5排序题 6填空题 7简答题：</text></view>
                  <input type="number" placeholder="请输入类别 1单选题 2多选题 3判断题 4连线题 5排序题 6填空题 7简答题" v-model="model.type"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">类型标识：</text></view>
                  <input  placeholder="请输入类型标识" v-model="model.identifier"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">选项（用于单选/多选）：</text></view>
                  <input  placeholder="请输入选项（用于单选/多选）" v-model="model.choices"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">正确答案：</text></view>
                  <input  placeholder="请输入正确答案" v-model="model.selected"/>
                </view>
              </view>
              <my-date label="创建时间：" fields="day" v-model="model.createAt" placeholder="请输入创建时间"></my-date>
              <my-date label="修改时间：" fields="day" v-model="model.updateAt" placeholder="请输入修改时间"></my-date>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">排序：</text></view>
                  <input type="number" placeholder="请输入排序" v-model="model.sort"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">左选项：</text></view>
                  <input  placeholder="请输入左选项" v-model="model.leftOptions"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">右选项：</text></view>
                  <input  placeholder="请输入右选项" v-model="model.rightOptions"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">连线题：</text></view>
                  <input  placeholder="请输入连线题" v-model="model.matchData"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">简答题小题目：</text></view>
                  <input  placeholder="请输入简答题小题目" v-model="model.questionShort"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">添加批次id(前端使用)：</text></view>
                  <input  placeholder="请输入添加批次id(前端使用)" v-model="model.batchId"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">extraFields：</text></view>
                  <input  placeholder="请输入extraFields" v-model="model.extraFields"/>
                </view>
              </view>
              <view class="cu-form-group">
                <view class="flex align-center">
                  <view class="title"><text space="ensp">answerDesc：</text></view>
                  <input  placeholder="请输入answerDesc" v-model="model.answerDesc"/>
                </view>
              </view>
				<view class="padding">
					<button class="cu-btn block bg-blue margin-tb-sm lg" @click="onSubmit">
						<text v-if="loading" class="cuIcon-loading2 cuIconfont-spin"></text>提交
					</button>
				</view>
			</form>
		</view>
    </view>
</template>

<script>
    import myDate from '@/components/my-componets/my-date.vue'

    export default {
        name: "DigitalTextbookQuestionForm",
        components:{ myDate },
        props:{
          formData:{
              type:Object,
              default:()=>{},
              required:false
          }
        },
        data(){
            return {
				CustomBar: this.CustomBar,
				NavBarColor: this.NavBarColor,
				loading:false,
                model: {},
                backRouteName:'index',
                url: {
                  queryById: "/digital_textbook_question/digitalTextbookQuestion/queryById",
                  add: "/digital_textbook_question/digitalTextbookQuestion/add",
                  edit: "/digital_textbook_question/digitalTextbookQuestion/edit",
                },
            }
        },
        created(){
             this.initFormData();
        },
        methods:{
           initFormData(){
               if(this.formData){
                    let dataId = this.formData.dataId;
                    this.$http.get(this.url.queryById,{params:{id:dataId}}).then((res)=>{
                        if(res.data.success){
                            console.log("表单数据",res);
                            this.model = res.data.result;
                        }
                    })
                }
            },
            onSubmit() {
                let myForm = {...this.model};
                this.loading = true;
                let url = myForm.id?this.url.edit:this.url.add;
				this.$http.post(url,myForm).then(res=>{
				   console.log("res",res)
				   this.loading = false
				   this.$Router.push({name:this.backRouteName})
				}).catch(()=>{
					this.loading = false
				});
            }
        }
    }
</script>
