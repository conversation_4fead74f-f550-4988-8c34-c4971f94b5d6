import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: 'uuid',
    align:"center",
    dataIndex: 'uuid'
   },
   {
    title: '云教材id',
    align:"center",
    dataIndex: 'textbookIdId'
   },
   {
    title: '章id',
    align:"center",
    dataIndex: 'chapterId'
   },
   {
    title: '问题',
    align:"center",
    dataIndex: 'question'
   },
   {
    title: '类别 1单选题 2多选题 3判断题 4连线题 5排序题 6填空题 7简答题',
    align:"center",
    dataIndex: 'type'
   },
   {
    title: '类型标识',
    align:"center",
    dataIndex: 'identifier'
   },
   {
    title: '选项（用于单选/多选）',
    align:"center",
    dataIndex: 'choices'
   },
   {
    title: '正确答案',
    align:"center",
    dataIndex: 'selected'
   },
   {
    title: '创建时间',
    align:"center",
    dataIndex: 'createAt',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '修改时间',
    align:"center",
    dataIndex: 'updateAt',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '排序',
    align:"center",
    dataIndex: 'sort'
   },
   {
    title: '左选项',
    align:"center",
    dataIndex: 'leftOptions'
   },
   {
    title: '右选项',
    align:"center",
    dataIndex: 'rightOptions'
   },
   {
    title: '连线题',
    align:"center",
    dataIndex: 'matchData'
   },
   {
    title: '简答题小题目',
    align:"center",
    dataIndex: 'questionShort'
   },
   {
    title: '添加批次id(前端使用)',
    align:"center",
    dataIndex: 'batchId'
   },
   {
    title: 'extraFields',
    align:"center",
    dataIndex: 'extraFields'
   },
   {
    title: 'answerDesc',
    align:"center",
    dataIndex: 'answerDesc'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: 'uuid',
    field: 'uuid',
    component: 'Input',
  },
  {
    label: '云教材id',
    field: 'textbookIdId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入云教材id!'},
          ];
     },
  },
  {
    label: '章id',
    field: 'chapterId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入章id!'},
          ];
     },
  },
  {
    label: '问题',
    field: 'question',
    component: 'InputTextArea',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入问题!'},
          ];
     },
  },
  {
    label: '类别 1单选题 2多选题 3判断题 4连线题 5排序题 6填空题 7简答题',
    field: 'type',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入类别 1单选题 2多选题 3判断题 4连线题 5排序题 6填空题 7简答题!'},
          ];
     },
  },
  {
    label: '类型标识',
    field: 'identifier',
    component: 'Input',
  },
  {
    label: '选项（用于单选/多选）',
    field: 'choices',
    component: 'InputTextArea',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入选项（用于单选/多选）!'},
          ];
     },
  },
  {
    label: '正确答案',
    field: 'selected',
    component: 'InputTextArea',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入正确答案!'},
          ];
     },
  },
  {
    label: '创建时间',
    field: 'createAt',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '修改时间',
    field: 'updateAt',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '排序',
    field: 'sort',
    component: 'InputNumber',
  },
  {
    label: '左选项',
    field: 'leftOptions',
    component: 'InputTextArea',
  },
  {
    label: '右选项',
    field: 'rightOptions',
    component: 'InputTextArea',
  },
  {
    label: '连线题',
    field: 'matchData',
    component: 'InputTextArea',
  },
  {
    label: '简答题小题目',
    field: 'questionShort',
    component: 'InputTextArea',
  },
  {
    label: '添加批次id(前端使用)',
    field: 'batchId',
    component: 'Input',
  },
  {
    label: 'extraFields',
    field: 'extraFields',
    component: 'InputTextArea',
  },
  {
    label: 'answerDesc',
    field: 'answerDesc',
    component: 'InputTextArea',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  uuid: {title: 'uuid',order: 0,view: 'text', type: 'string',},
  textbookIdId: {title: '云教材id',order: 1,view: 'text', type: 'string',},
  chapterId: {title: '章id',order: 2,view: 'text', type: 'string',},
  question: {title: '问题',order: 3,view: 'textarea', type: 'string',},
  type: {title: '类别 1单选题 2多选题 3判断题 4连线题 5排序题 6填空题 7简答题',order: 4,view: 'number', type: 'number',},
  identifier: {title: '类型标识',order: 5,view: 'text', type: 'string',},
  choices: {title: '选项（用于单选/多选）',order: 6,view: 'textarea', type: 'string',},
  selected: {title: '正确答案',order: 7,view: 'textarea', type: 'string',},
  createAt: {title: '创建时间',order: 8,view: 'date', type: 'string',},
  updateAt: {title: '修改时间',order: 9,view: 'date', type: 'string',},
  sort: {title: '排序',order: 10,view: 'number', type: 'number',},
  leftOptions: {title: '左选项',order: 11,view: 'textarea', type: 'string',},
  rightOptions: {title: '右选项',order: 12,view: 'textarea', type: 'string',},
  matchData: {title: '连线题',order: 13,view: 'textarea', type: 'string',},
  questionShort: {title: '简答题小题目',order: 14,view: 'textarea', type: 'string',},
  batchId: {title: '添加批次id(前端使用)',order: 15,view: 'text', type: 'string',},
  extraFields: {title: 'extraFields',order: 16,view: 'textarea', type: 'string',},
  answerDesc: {title: 'answerDesc',order: 17,view: 'textarea', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}