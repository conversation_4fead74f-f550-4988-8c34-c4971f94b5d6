<#include "/common/utils.ftl">
<#list subTables as subTab>
#segment#${subTab.entityName}.java
package ${bussiPackage}.${entityPackage}.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.UnsupportedEncodingException;

/**
 * @Description: ${subTab.ftlDescription}
 * @Author: jeecg-boot
 * @Date:   ${.now?string["yyyy-MM-dd"]}
 * @Version: V1.0
 */
@Data
@TableName("${subTab.tableName}")
@ApiModel(value="${subTab.tableName}对象", description="${subTab.ftlDescription}")
public class ${subTab.entityName} implements Serializable {
    private static final long serialVersionUID = 1L;

    <#assign excel_ignore_arr=['createBy','createTime','updateBy','updateTime','sysOrgCode']>
     <#assign excel_ignore_classType_arr=['pca','switch','cat_tree']>
<#list subTab.originalColumns as po>
	/**${po.filedComment}*/
<#if po.fieldName == primaryKeyField>
	@TableId(type = IdType.ASSIGN_ID)
<#else>
	<#if po.fieldDbType =='Date' || po.fieldDbType =='Datetime'>
		<#if po.classType=='date'>
    <#if !excel_ignore_arr?seq_contains("${po.fieldName}")>
	@Excel(name = "${po.filedComment}", width = 15, format = "yyyy-MM-dd")
	</#if>
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
		<#else>
    <#if !excel_ignore_arr?seq_contains("${po.fieldName}")>
	@Excel(name = "${po.filedComment}", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	</#if>
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
		</#if>
	<#elseif !subTab.foreignKeys?seq_contains(po.fieldName?cap_first)>
    <#if !excel_ignore_arr?seq_contains("${po.fieldName}")  && !excel_ignore_classType_arr?seq_contains("${po.classType}")>
	@Excel(name = "${po.filedComment}", width = 15)
	</#if>
	</#if>
</#if>
<#if po.classType =='list' || po.classType =='radio' || po.classType =='list_multi' || po.classType =='checkbox' || po.classType =='sel_search'>
  <#if po.dictTable?default("")?trim?length gt 1>
    @Dict(dicCode = "${po.dictField}",dicText = "${po.dictText}",dictTable = "${po.dictTable}")
  <#elseif po.dictField?default("")?trim?length gt 1>
    @Dict(dicCode = "${po.dictField}")
  </#if>
</#if>
<#if po.classType =='cat_tree'>
    //@Dict(dicCode = "id",dicText = "name",dictTable = "sys_category")
</#if>
<#if po.classType =='sel_depart'>
    <#assign list_field_dictCode='dicCode = "${camelToDashed(po.extendParams.store?default(\"id\")?trim)}", dicText = "${camelToDashed(po.extendParams.text?default(\"depart_name\")?trim)}", dictTable = "sys_depart"'>
    @Dict(${list_field_dictCode})
</#if>
	<#-- 大字段转换 -->
    <#include "/common/blob.ftl">
</#list>
}
</#list>