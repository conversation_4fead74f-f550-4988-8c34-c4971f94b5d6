import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/${entityPackagePath}/${entityName?uncap_first}/list',
  save= '/${entityPackagePath}/${entityName?uncap_first}/add',
  edit= '/${entityPackagePath}/${entityName?uncap_first}/edit',
  deleteOne = '/${entityPackagePath}/${entityName?uncap_first}/delete',
  deleteBatch = '/${entityPackagePath}/${entityName?uncap_first}/deleteBatch',
  importExcel = '/${entityPackagePath}/${entityName?uncap_first}/importExcel',
  exportXls = '/${entityPackagePath}/${entityName?uncap_first}/exportXls',
<#list subTables as sub><#rt/>
  ${sub.entityName?uncap_first}List = '/${entityPackagePath}/${entityName?uncap_first}/list${sub.entityName}ByMainId',
  ${sub.entityName?uncap_first}Save= '/${entityPackagePath}/${entityName?uncap_first}/add${sub.entityName}',
  ${sub.entityName?uncap_first}Edit= '/${entityPackagePath}/${entityName?uncap_first}/edit${sub.entityName}',
  ${sub.entityName?uncap_first}Delete = '/${entityPackagePath}/${entityName?uncap_first}/delete${sub.entityName}',
  ${sub.entityName?uncap_first}DeleteBatch = '/${entityPackagePath}/${entityName?uncap_first}/deleteBatch${sub.entityName}',
</#list>
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params },{ isTransformResponse: false });
}
<#list subTables as sub><#rt/>
  <#assign myForeignKeys=''>
  <#list sub.foreignKeys as key>
    <#assign myForeignKeys='${key?uncap_first}'>
  </#list>
  
/**
 * 列表接口
 * @param params
 */
export const ${sub.entityName?uncap_first}List = (params) => {
  if(params['${myForeignKeys}']){
    return defHttp.get({ url: Api.${sub.entityName?uncap_first}List, params });
  }
  return Promise.resolve({});
}

/**
 * 删除单个
 */
export const ${sub.entityName?uncap_first}Delete = (params,handleSuccess) => {
  return defHttp.delete({ url: Api.${sub.entityName?uncap_first}Delete, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 */
export const ${sub.entityName?uncap_first}DeleteBatch = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.${sub.entityName?uncap_first}DeleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 */
export const  ${sub.entityName?uncap_first}SaveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.${sub.entityName?uncap_first}Edit : Api.${sub.entityName?uncap_first}Save;
  return defHttp.post({ url: url, params },{ isTransformResponse: false });
}

/**
 * 导入
 */
export const ${sub.entityName?uncap_first}ImportUrl = '/${entityPackagePath}/${entityName?uncap_first}/import${sub.entityName}'

/**
 * 导出
 */
export const ${sub.entityName?uncap_first}ExportXlsUrl = '/${entityPackagePath}/${entityName?uncap_first}/export${sub.entityName}'
</#list>