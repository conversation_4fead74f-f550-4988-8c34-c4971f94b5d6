<#include "/common/utils.ftl">
<template>
  <div>
  <!-- 子表单区域 -->
    <a-tabs v-model:activeKey="activeKey" animated @change="handleChangeTabs">
     <!--主表区域 -->
     <a-tab-pane tab="${tableVo.ftlDescription}" :key="refKeys[0]" :forceRender="true" :style="tabsStyle">
       <BasicForm @register="registerForm" ref="formRef"/>
     </a-tab-pane>
  <!--子表单区域 -->
<#list subTables as sub><#rt/>
  <#assign refKey = sub.entityName?uncap_first/>
  <#if sub.foreignRelationType =='1'>
      <a-tab-pane tab="${sub.ftlDescription}" key="${refKey}" :forceRender="true" :style="tabsStyle">
        <${sub.entityName}Form ref="${sub.entityName?uncap_first}Form" :disabled="formDisabled"></${sub.entityName}Form>
      </a-tab-pane>

  <#else>
      <a-tab-pane tab="${sub.ftlDescription}" key="${refKey}" :forceRender="true" :style="tabsStyle">
        <JVxeTable
          keep-source
          resizable
          ref="${refKey}"
          v-if="${sub.entityName?uncap_first}Table.show"
          :loading="${sub.entityName?uncap_first}Table.loading"
          :columns="${sub.entityName?uncap_first}Table.columns"
          :dataSource="${sub.entityName?uncap_first}Table.dataSource"
          :height="340"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :toolbar="true"
          />
      </a-tab-pane>
  </#if>
</#list>
    </a-tabs>

    <div style="width: 100%;text-align: center;margin-top: 10px;" v-if="showFlowSubmitButton">
      <a-button preIcon="ant-design:check-outlined" style="width: 126px" type="primary" @click="handleSubmit">提 交</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
    import { defHttp } from '/@/utils/http/axios';
    import {ref, computed, unref,reactive, onMounted, defineProps } from 'vue';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import { JVxeTable } from '/@/components/jeecg/JVxeTable'
    import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods.ts'
    <#list subTables as sub>
      <#if sub.foreignRelationType =='1'>
    import ${sub.entityName}Form from './${sub.entityName}Form.vue'
      </#if>
    </#list>
    import {formSchema<#list subTables as sub><#if sub.foreignRelationType =='0'>,${sub.entityName?uncap_first}Columns</#if></#list>} from '../${entityName}.data';
    import {saveOrUpdate<#list subTables as sub>,${sub.entityName?uncap_first}List</#list>} from '../${entityName}.api';
    import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils'
    const refKeys = ref(['${tableVo.entityName?uncap_first}',<#list subTables as sub>'${sub.entityName?uncap_first}', </#list>]);
    <#assign hasOne2Many = false>
    <#assign hasOne2One = false>
    const activeKey = ref('${tableVo.entityName?uncap_first}');
<#list subTables as sub>
<#if sub.foreignRelationType =='0'>
 <#assign hasOne2Many = true>
    const ${sub.entityName?uncap_first} = ref();
</#if>
<#if sub.foreignRelationType =='1'>
 <#assign hasOne2One = true>
    const ${sub.entityName?uncap_first}Form = ref();
</#if>
</#list>
    const tableRefs = {<#list subTables as sub><#if sub.foreignRelationType =='0'>${sub.entityName?uncap_first}, <#assign hasOne2Many = true></#if></#list>};
   <#list subTables as sub>
   <#if sub.foreignRelationType =='0'>
    const ${sub.entityName?uncap_first}Table = reactive({
          loading: false,
          dataSource: [],
          columns:${sub.entityName?uncap_first}Columns,
          show: false
    })
    </#if>
   </#list>

    const props = defineProps({
      formData: { type: Object, default: ()=>{} },
      formBpm: { type: Boolean, default: true }
    });
    const formDisabled = computed(()=>{
      if(props.formBpm === true){
        if(props.formData.disabled === false){
          return false;
        }
      }
      return true;
    });
    // 是否显示提交按钮
    const showFlowSubmitButton = computed(()=>{
      if(props.formBpm === true){
        if(props.formData.disabled === false){
          return true
        }
      }
      return false
    });
    
    //表单配置
    const [registerForm, {setProps,resetFields, setFieldsValue, validate}] = useForm({
        labelWidth: 150,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: {span: ${getFormSpan(tableVo.fieldRowNum?default(1))}}
    });

    onMounted(()=>{
      initFormData();
    });
    //渲染流程表单数据
    const queryByIdUrl = '/${entityPackagePath}/${entityName?uncap_first}/queryById';
    async function initFormData(){
      if(props.formBpm === true){
        await reset();
        let params = {id: props.formData.dataId};
        const data = await defHttp.get({url: queryByIdUrl, params});
        //表单赋值
        await setFieldsValue({
          ...data
        });
        <#list subTables as sub><#rt/>
        <#if sub.foreignRelationType =='1'>
        ${sub.entityName?uncap_first}Form.value.initFormData(${sub.entityName?uncap_first}List, data.id)
        </#if>
        </#list>
        <#list subTables as sub><#rt/>
        <#if sub.foreignRelationType =='0'>
        requestSubTableData(${sub.entityName?uncap_first}List, {id: data.id}, ${sub.entityName?uncap_first}Table, ()=>{
          ${sub.entityName?uncap_first}Table.show = true;
        })
        </#if>
        </#list>
        // 隐藏底部时禁用整个表单
        setProps({ disabled: formDisabled.value })
      }
    }

    //方法配置
    const [handleChangeTabs,handleSubmit,requestSubTableData,formRef] = useJvxeMethod(requestAddOrEdit,classifyIntoFormData,tableRefs,activeKey,refKeys<#if hasOne2One==true>,validateSubForm</#if>);
    // 弹窗tabs滚动区域的高度
    const tabsStyle = computed(() => {
      let height: Nullable<string> = null
      let minHeight = '100px'
      // 弹窗wrapper
      <#-- update-begin-author:taoyan date:2022-11-14 for:VUEN-2674 【代码生成】对接流程表单 附加单据显示问题 5.多tab生成代码后，新增 没有滚动条，只能填写部分字段 --> 
      let overflow = 'auto';
      return {height, minHeight, overflow};
      <#-- update-end-author:taoyan date:2022-11-14 for:VUEN-2674 【代码生成】对接流程表单 附加单据显示问题 5.多tab生成代码后，新增 没有滚动条，只能填写部分字段 -->
    })

    async function reset(){
      await resetFields();
      activeKey.value = '${tableVo.entityName?uncap_first}';
      <#list subTables as sub>
        <#if sub.foreignRelationType =='0'>
      ${sub.entityName?uncap_first}Table.dataSource = [];
        </#if>
        <#if sub.foreignRelationType =='1'>
      ${sub.entityName?uncap_first}Form.value.resetFields();
        </#if>
      </#list>
    }
    function classifyIntoFormData(allValues) {
         let main = Object.assign({}, allValues.formValue)
         return {
           ...main, // 展开
 <#assign subManyIndex = 0>
 <#list subTables as sub><#rt/>
 <#if sub.foreignRelationType =='0'>
           ${sub.entityName?uncap_first}List: allValues.tablesValue[${subManyIndex}].tableData,
           <#assign subManyIndex = subManyIndex+1>
 <#else>
           ${sub.entityName?uncap_first}List: ${sub.entityName?uncap_first}Form.value.getFormData(),
 </#if>
 </#list>
         }
       }
      <#if hasOne2One==true>
     //校验所有一对一子表表单
     function validateSubForm(allValues){
         return new Promise((resolve,reject)=>{
             Promise.all([
             <#list subTables as sub><#rt/>
            <#if sub.foreignRelationType =='1'>
                  ${sub.entityName?uncap_first}Form.value.validateForm(${sub_index+1}),
            </#if>
          </#list>
             ]).then(() => {
                 resolve(allValues)
             }).catch(e => {
                 if (e.error === VALIDATE_FAILED) {
                     // 如果有未通过表单验证的子表，就自动跳转到它所在的tab
                     activeKey.value = e.index == null ? unref(activeKey) : refKeys.value[e.index]
                 } else {
                     console.error(e)
                 }
             })
         })
     }
    </#if>
    //表单提交事件
    async function requestAddOrEdit(values) {
      //提交表单
      await saveOrUpdate(values, true);
    }
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>

<style lang="less">
// Online表单Tab风格专属样式
.j-cgform-tab-modal {
  .ant-modal-header {
    padding-top: 8px;
    padding-bottom: 8px;
    border-bottom: none !important;
  }

  .ant-modal .ant-modal-body > .scrollbar,
  .ant-tabs-nav .ant-tabs-tab {
    padding-top: 0;
  }

  .ant-tabs-top-bar {
    width: calc(100% - 55px);
    position: relative;
    left: -14px;
  }

  .ant-tabs .ant-tabs-top-content > .ant-tabs-tabpane {
    overflow: hidden auto;
  }
}
</style>