package ${bussiPackage}.entity.${entityPackage};

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: ${tableVo.ftlDescription}
 * @Author: jeecg-boot
 * @Date:   ${.now?string["yyyy-MM-dd"]}
 * @Version: V1.0
 */
@Data
@TableName("${tableName}")
@ApiModel(value="${tableName}对象", description="${tableVo.ftlDescription}")
public class ${entityName} implements Serializable {
    private static final long serialVersionUID = 1L;
    
    <#list originalColumns as po>
	/**${po.filedComment}*/
	<#if po.fieldName == primaryKeyField>
	@TableId(type = IdType.ASSIGN_ID)
	<#else>
    <#if po.fieldType =='java.util.Date'>
      <#if po.fieldDbType =='date'>
    @Excel(name = "${po.filedComment}", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
      <#elseif po.fieldDbType =='datetime'>
    @Excel(name = "${po.filedComment}", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
      </#if>
    <#else>
    @Excel(name = "${po.filedComment}", width = 15)
    </#if>
  </#if>
	@ApiModelProperty(value = "${po.filedComment}")
	private <#if po.fieldType=='java.sql.Blob'>byte[]<#else>${po.fieldType}</#if> ${po.fieldName};
	</#list>
}
